import { PROBLEMS_SHOW, SEVERITIES } from '@/enums';
import withStorageToUrl from '@/hoc/withSyncToUrl';
import { alarmFindByIds, alarmPage } from '@/services/http/alarm';
import { formatSecondsToString, option2enum } from '@/utils';
import SearchOptionRender from '@/utils/SearchOptionRender';
import { defaultTableConfig } from '@/utils/setting';
import {
  ActionType,
  PageContainer,
  ProColumns,
  ProConfigProvider,
  ProFormInstance,
  ProTable,
} from '@ant-design/pro-components';
import { history, useRequest } from '@umijs/max';

import { Badge, Space, Tag, Tooltip } from 'antd';
import { isMicroservice } from 'config/defaultSettings';
import dayjs from 'dayjs';
import { isArray } from 'lodash';
import React, { useMemo, useRef, useState } from 'react';
import { valueTypeMap } from '../HostMonitor/valueTypeMap';
import ActionModal from './components/ActionModal';
import MarkdownModal from './components/MarkdownModal';
import UpdateDrawer from './components/UpdateDrawer';

// 自定义的 syncToUrl 函数，过滤掉 undefined 值
const customSyncToUrl = (params: Record<string, any>) => {
  const queryParams = new URLSearchParams();

  Object.entries(params).forEach(([key, val]) => {
    // 过滤掉 undefined、null、空字符串和 'undefined' 字符串
    if (val === undefined || val === null || val === '' || val === 'undefined') {
      return;
    }

    if (isArray(val)) {
      // 过滤数组中的无效值
      const validItems = (val as any[]).filter(
        (item: any) => item !== undefined && item !== null && item !== '' && item !== 'undefined',
      );

      // 只有当数组中有有效值时才添加到URL
      if (validItems.length > 0) {
        validItems.forEach((item: any) => {
          queryParams.append(key, String(item));
        });
      }
    } else {
      queryParams.append(key, String(val));
    }
  });

  const pathname = window.location.pathname;
  const basePath = isMicroservice ? '/' + pathname.split('/').slice(2).join('/') : pathname;

  const searchUrl = queryParams.toString();
  history.push(`${basePath}?${searchUrl}`);
  window.localStorage.setItem(basePath, searchUrl);
};

const Alarm: React.FC = withStorageToUrl(({ queryParams }) => {
  const tableRef = useRef<ActionType | undefined>();
  const formRef = useRef<ProFormInstance>();
  const [modalVisit, setModalVisit] = useState(false);
  const [markdownModalVisit, setMarkdownModalVisit] = useState(false);
  const [drawerVisit, setDrawerVisit] = useState(false);
  const [content, setContent] = useState('');
  const actionRef = useRef<any[]>();
  const problemRef = useRef<RK_API.Problem>();
  const recoverRef = useRef<boolean | undefined>(undefined);

  if (queryParams.get('showStyle') === null) {
    formRef.current?.setFieldValue('showStyle', '0');
  }

  const timestampArr = useMemo(() => {
    const dateArr = queryParams.getAll('date');
    if (!dateArr.length) return [];
    const [startDate, endDate] = dateArr;
    return [dayjs(startDate).startOf('D'), dayjs(endDate).endOf('D')];
  }, [queryParams.getAll('date')]);

  // 获取AI分析
  const { run: getAI } = useRequest((ids) => alarmFindByIds({ ids }), {
    manual: true,
    onSuccess: (response: any) => {
      setContent(response?.data?.[0]?.suggestion || '');
      setMarkdownModalVisit(true);
    },
  });

  const columns: ProColumns<RK_API.Problem>[] = [
    {
      title: '显示',
      colSize: 4,
      dataIndex: 'showStyle',
      hideInTable: true,
      valueType: 'radio',
      fieldProps: {
        options: PROBLEMS_SHOW,
        optionType: 'button',
        buttonStyle: 'solid',
        onChange(e) {
          if (e.target.value === '0') {
            formRef.current?.setFieldValue('alarmTime', []);
          } else {
            formRef.current?.setFieldValue('recovered', recoverRef.current);
          }
          setTimeout(() => {
            formRef.current?.submit();
          }, 500);
        },
      },
      initialValue: queryParams.get('showStyle') || '0',
    },
    {
      title: '告警对象',
      dataIndex: 'objectIds',
      hideInTable: true,
      valueType: 'text',
      fieldProps: {
        placeholder: '请输入告警对象，多个用逗号分隔',
      },
      search: {
        transform: (value: string) => {
          if (!value) return {};
          // 将逗号分隔的字符串转换为数组
          const objectIds = value
            .split(',')
            .map((id) => id.trim())
            .filter(Boolean);
          return objectIds.length > 0 ? { objectIds } : {};
        },
      },
      initialValue: queryParams.getAll('objectIds')?.join(',') || '',
    },
    {
      title: '告警内容',
      dataIndex: 'message',
      hideInTable: true,
      valueType: 'text',
      fieldProps: {
        placeholder: '请输入告警内容',
      },
      initialValue: queryParams.get('message') || '',
    },
    {
      title: '恢复状态',
      dataIndex: 'recovered',
      hideInTable: true,
      valueType: 'select',
      fieldProps: {
        options: [
          { label: '全部', value: undefined },
          { label: '已恢复', value: true },
          { label: '未恢复', value: false },
        ],
        placeholder: '请选择恢复状态',
        allowClear: true,
        onChange: (value: boolean | undefined) => {
          recoverRef.current = value;
        },
      },
      initialValue: (() => {
        const recovered = queryParams.get('recovered');
        if (recovered === 'true') return true;
        if (recovered === 'false') return false;
        return undefined;
      })(),
    },
    {
      title: '告警时间',
      dataIndex: 'alarmTime',
      hideInTable: true,
      valueType: 'dateTimeRange',
      fieldProps: {
        placeholder: ['开始时间', '结束时间'],
        showTime: true,
        format: 'YYYY-MM-DD HH:mm:ss',
        presets: [
          { label: '本日', value: [dayjs().startOf('day'), dayjs().endOf('day')] },
          { label: '本周', value: [dayjs().startOf('isoWeek'), dayjs().endOf('isoWeek')] },
          { label: '本月', value: [dayjs().startOf('month'), dayjs().endOf('month')] },
          { label: '近三个月', value: [dayjs().subtract(3, 'month'), dayjs()] },
          { label: '近半年', value: [dayjs().subtract(6, 'month'), dayjs()] },
        ],
      },
      search: {
        transform: (value: any) => {
          if (!value || !value[0] || !value[1]) {
            return {};
          }
          // 确保转换为 dayjs 对象
          const startTime = dayjs(value[0]).format('YYYY-MM-DD HH:mm:ss');
          const endTime = dayjs(value[1]).format('YYYY-MM-DD HH:mm:ss');
          return {
            startTime,
            endTime,
          };
        },
      },
      initialValue: timestampArr.length > 0 ? timestampArr : undefined,
    },
    {
      title: '告警等级',
      dataIndex: 'level',
      colSize: 2,
      hideInTable: true,
      valueType: 'checkbox',
      fieldProps: {
        options: SEVERITIES,
      },
      search: {
        transform: (value: string[]) => {
          // AlarmPageRequest.level 只支持单个等级，取第一个选中的
          if (!value || value.length === 0) return {};
          return { level: value[0] as '5' | '4' | '3' | '2' | '1' };
        },
      },
      initialValue: queryParams.getAll('level') || [],
    },
    {
      title: '标签',
      dataIndex: 'tagIds',
      hideInTable: true,
      valueType: 'select',
      fieldProps: {
        mode: 'multiple',
        placeholder: '请选择标签',
        options: [], // 暂时为空，后面再实现
        allowClear: true,
      },
      search: {
        transform: (value: string[]) => {
          return value && value.length > 0 ? { tagIds: value } : {};
        },
      },
      initialValue: [],
    },

    {
      title: '告警时间',
      dataIndex: 'clock',
      hideInSearch: true,
      width: 160,
      renderText(text) {
        return dayjs.unix(text).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: '告警等级',
      dataIndex: 'severity',
      valueEnum: option2enum(SEVERITIES),
      hideInSearch: true,
      width: 100,
      render: (text, record) => {
        if (!text) return text;
        const severityObj = SEVERITIES.find((item) => record.severity === item.value);
        return severityObj ? (
          <Tooltip title={severityObj.label}>
            <Tag color={severityObj.tagColor}>{text}</Tag>
          </Tooltip>
        ) : (
          text
        );
      },
    },
    {
      title: '告警对象',
      dataIndex: 'objectid',
      hideInSearch: true,
      width: 180,
      render: (objectId, entity) => {
        const hostName = (entity as any)?.hosts?.at(0)?.name || '-';
        if (hostName === '-') return hostName;

        return (
          <a
            onClick={() => {
              // 跳转到监控对象详情页面
              history.push(`/monitor-config/host/details/${objectId}`);
            }}
          >
            {hostName}
          </a>
        );
      },
    },
    {
      title: '告警内容',
      dataIndex: 'name',
      width: 300,
      hideInSearch: true,
      render(dom, entity) {
        return (
          <a
            onClick={() => {
              history.push(`/alarm/event/details/${entity.eventid}`);
            }}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: '告警时长',
      dataIndex: 'clock',
      hideInSearch: true,
      width: 120,
      renderText(text, entity) {
        if (entity?.r_eventid === '0' || !entity?.r_clock) {
          const now = dayjs().unix();
          // 未解决的告警，使用当前时间减去告警开始时间
          const seconds = now - text;
          return formatSecondsToString(seconds);
        } else {
          // 已解决的告警，使用恢复时间减去告警开始时间
          const seconds = Number(entity.r_clock) - text;
          return formatSecondsToString(seconds);
        }
      },
    },
    {
      title: '确认告警',
      dataIndex: 'acknowledged',
      hideInSearch: true,
      width: 100,
      renderText(text) {
        return text === '1' ? '是' : '否';
      },
    },
    {
      title: '恢复状态',
      dataIndex: 'r_eventid',
      hideInSearch: true,
      width: 100,
      render: (_, entity) => {
        if (Number(entity?.r_eventid) > 0) {
          return <Badge status="success" text="已恢复" />;
        }
        return <Badge status="error" text="未恢复" />;
      },
    },
    {
      title: '恢复时间',
      dataIndex: 'r_clock',
      hideInSearch: true,
      hideInTable: queryParams.get('showStyle') !== '1',
      width: 160,
      renderText(text) {
        if (!text || text === 0) return '-';
        return dayjs.unix(text).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: '标签',
      dataIndex: 'tags',
      hideInSearch: true,
      width: 200,
      render(_, entity) {
        const { tags = [], inheritedTags = [] } = entity;
        const mixTags = [...inheritedTags, ...tags];
        if (!mixTags.length) return <>-</>;
        return (mixTags as RK_API.TemplateTag[]).map((item, index) => (
          <Tag key={index}>
            {item?.tag}: {item?.value}
          </Tag>
        ));
      },
    },
    {
      title: '操作',
      width: 130,
      key: 'option',
      valueType: 'option',
      align: 'center',
      fixed: 'right',
      render: (_, record) => {
        const hasDeepSeek = record?.tags?.find(
          (item: RK_API.TemplateTag) => item.tag === 'rkmview_gjsj_ai' && item.value === 'deepseek',
        );

        return (
          <Space>
            {hasDeepSeek && (
              <a
                onClick={() => {
                  getAI([record.eventid]);
                }}
              >
                AI分析
              </a>
            )}
            <a
              onClick={() => {
                setDrawerVisit(true);
                problemRef.current = record;
              }}
            >
              确认
            </a>
          </Space>
        );
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProConfigProvider valueTypeMap={valueTypeMap}>
        <ProTable<RK_API.Problem>
          {...defaultTableConfig}
          search={SearchOptionRender}
          onSubmit={customSyncToUrl}
          rowKey="eventid"
          formRef={formRef}
          actionRef={tableRef}
          columns={columns}
          headerTitle="告警列表"
          request={async (params) => {
            const {
              message,
              objectIds,
              startTime,
              endTime,
              level,
              tagIds,
              recovered = recoverRef.current,
              current = 1,
              pageSize = 10,
            } = params;

            // 使用新的 alarmPage 接口，直接传递参数
            const alarmPageParams: API.AlarmPageRequest = {
              message,
              objectIds,
              startTime,
              endTime,
              level,
              tagIds,
              recovered,
              page: {
                page: current,
                size: pageSize,
              },
            };

            const result = await alarmPage(alarmPageParams);

            // 转换数据格式以保持兼容性
            const convertedData =
              result.data?.data?.map(
                (item: API.AlarmPageVO) =>
                  ({
                    eventid: item.id,
                    severity: item.level,
                    objectid: item.objects?.[0]?.objectId,
                    name: item.message,
                    suppressed: '0',
                    clock: dayjs(item.time).unix(),
                    r_eventid: item.recovered ? '1' : '0',
                    acknowledged: item.confirm ? '1' : '0',
                    hosts: item.objects?.map((obj) => ({ name: obj.objectName })),
                    tags: [],
                    inheritedTags: [],
                    // 添加缺失的字段以满足 RK_API.Problem 类型
                    source: '0',
                    object: '0',
                    ns: 0,
                    r_clock: item.recoverTime ? dayjs(item.recoverTime).unix() : 0,
                    userid: '',
                    correlationId: '',
                    value: '1',
                    acknowledges: [],
                  } as unknown as RK_API.Problem & { hosts?: any[] }),
              ) || [];

            const problem = {
              data: convertedData,
              success: true,
              total: parseInt(result.data?.total || '0'),
            };
            const resultData = problem.data || [];

            return {
              data: resultData,
              success: true,
              total: parseInt(result.data?.total || '0'),
            };
          }}
        />
      </ProConfigProvider>
      {/* 动作 */}
      <ActionModal
        onOpenChange={setModalVisit}
        open={modalVisit}
        initialValues={{
          list: actionRef.current,
        }}
      />
      {/* 更新问题 */}
      <UpdateDrawer
        open={drawerVisit}
        onOpenChange={setDrawerVisit}
        onFinish={async () => tableRef.current?.reload()}
        initialValues={problemRef.current}
      />
      {/* markdown内容弹窗 */}
      <MarkdownModal
        setMarkdownModalVisit={setMarkdownModalVisit}
        open={markdownModalVisit}
        content={content}
      />
    </PageContainer>
  );
});

export default Alarm;
