.tabs-card {
  background-color: transparent;
  :global {
    .ant-tabs-nav {
      background-color: #fff;
      border-radius: 4px 4px 0 0;
    }
    .ant-tabs-tabpane {
      .ant-pro-card:first-of-type {
        .ant-pro-card-body:first-of-type {
          display: block;
          padding: 0;
          border-radius: 0;
        }
        background-color: transparent;
      }
    }
    .ant-row,
    .ant-col {
      height: 100%;
    }
  }
}
