import { INTERFACE_TYPE, SEVERITIES, STATUS } from '@/enums';
import withStorageToUrl from '@/hoc/withSyncToUrl';
import { getAllMonitoringObjectType } from '@/services/http/monitoringObject';
import { zabbix, zabbixList } from '@/services/zabbix';
import {
  getTagData,
  groupedData,
  option2enum,
  passParamsToPage,
  queryPagingTable,
  syncToUrl,
} from '@/utils';
import { hostGroups } from '@/utils/column';
import SearchOptionRender from '@/utils/SearchOptionRender';
import { defaultTableConfig } from '@/utils/setting';
import {
  ActionType,
  PageContainer,
  ProColumns,
  ProConfigProvider,
  ProFormInstance,
  ProTable,
} from '@ant-design/pro-components';
import { Link, useRequest } from '@umijs/max';
import { Tag } from 'antd';
import React, { useRef, useState } from 'react';
import { getColor } from '../Host';
import InterfaceModal from '../Host/components/InterfaceModal';
import { valueTypeMap } from './valueTypeMap';

const HostMonitor: React.FC = withStorageToUrl(({ queryParams }) => {
  const tableRef = useRef<ActionType | undefined>();
  const formRef = useRef<ProFormInstance>();
  const interfaceArr = useRef<{ interfaces: RK_API.Interface[] }>();
  const [modalVisit, setModalVisit] = useState(false);

  // 获取所有对象
  const { data: objectList = [], loading: objectListLoading } = useRequest(
    getAllMonitoringObjectType,
    {
      formatResult(res) {
        if (res?.code === 200) {
          return res?.data?.map((item) => ({
            value: item.id,
            label: item.name,
            options: item?.subTypes?.map((item) => ({
              value: item.id,
              label: item.name,
            })),
            ...item,
          }));
        }
        return [];
      },
    },
  );

  // 获取监控列表
  const { run: getHosts } = useRequest((params) => zabbixList(params), {
    manual: true,
  });

  // 获取问题
  const { run: getProblemList, data: problemList } = useRequest(
    (ids) =>
      zabbixList({
        method: 'problem.get',
        objectids: ids,
      }),
    {
      manual: true,
    },
  );

  // 返回所有监控对象的标签
  const { data: hostTags } = useRequest(
    () =>
      zabbix({
        method: 'host.get',
        output: ['tags'],
        selectTags: 'extend',
        inheritedTags: true,
        selectInheritedTags: 'extend',
      }),
    {
      formatResult: (res) => {
        if (res.code === 200) {
          return getTagData(res.data);
        }
      },
    },
  );

  // 获取trigger
  const { run: getTriggers, data: triggerList } = useRequest(
    (hostids) =>
      zabbixList({
        method: 'trigger.get',
        hostids,
        monitored: '1',
        skipDependent: true, // 跳过处于问题状态且依赖于其他触发器的触发器
        selectHosts: 'extend',
        output: ['triggerid', 'value'],
      }),
    {
      manual: true,
      onSuccess: (res: RK_API.Trigger[]) => {
        const problemIds = res?.filter((item) => item.value === '1')?.map((item) => item.triggerid);
        getProblemList(problemIds);
      },
    },
  );

  // 表格
  const columns: ProColumns<RK_API.Host>[] = [
    {
      title: '对象名称',
      dataIndex: 'name',
      width: 150,
      fixed: 'left',
      initialValue: queryParams.get('name'),
      render: (dom, record) => <Link to={`/monitor/host/details/${record.hostid}`}>{dom}</Link>,
    },
    { ...hostGroups, initialValue: queryParams.getAll('groupids'), title: '对象群组' },
    {
      dataIndex: 'rkzl_monitorObjectSubtypeId',
      title: '类型',
      width: 120,
      valueType: 'select',
      fieldProps: {
        options: objectList,
        loading: objectListLoading,
      },
      hideInTable: true,
    },
    {
      dataIndex: ['rkzl_type', 'name'],
      title: '类型',
      width: 120,
      hideInSearch: true,
    },
    {
      dataIndex: ['rkzl_subtype', 'name'],
      title: '子类型',
      width: 100,
      hideInSearch: true,
    },
    // {
    //   title: '接口',
    //   width: 100,
    //   dataIndex: 'ip',
    //   renderText(text, entity) {
    //     const { interfaces = [] } = entity;
    //     if (interfaces.length === 0) return '-';
    //     const res = interfaces.at(0);
    //     return `${res?.ip}${res?.port}`;
    //   },
    //   hideInSearch: true,
    // },
    {
      title: '可用性',
      width: 150,
      dataIndex: 'interfaces',
      hideInSearch: true,
      tooltip: '点击tag查看接口详情',
      render(dom, entity) {
        const { interfaces = [] } = entity;
        const data = groupedData(interfaces, 'type');
        return Object.entries(data).map(([type, arr]) => {
          const color = getColor(arr);
          return (
            <Tag
              key={arr?.[0].interfaceid}
              color={color}
              onClick={() => {
                setModalVisit(true);
                interfaceArr.current = { interfaces: arr };
              }}
            >
              {option2enum(INTERFACE_TYPE)[type]?.text}
            </Tag>
          );
        });
      },
    },

    {
      title: '状态',
      width: 100,
      dataIndex: 'status',
      valueEnum: option2enum(STATUS),
      initialValue: queryParams.get('status'),
    },
    {
      title: '监控数据',
      width: 100,
      dataIndex: 'items',
      hideInSearch: true,
      render(dom, entity) {
        const { items = [] } = entity;
        return items?.length ? (
          <a
            onClick={() => {
              passParamsToPage('/monitor/list', {
                hostids: entity.hostid,
              });
            }}
          >
            {items.length}项
          </a>
        ) : (
          <>-</>
        );
      },
    },

    {
      title: '告警事件',
      dataIndex: 'triggers',
      hideInSearch: true,
      render(dom, entity) {
        const { hostid } = entity;
        const arr: string[] =
          triggerList
            ?.filter(
              (item: RK_API.Trigger) =>
                item.value === '1' && item?.hosts?.some((item) => item.hostid === hostid),
            )
            ?.map((item: RK_API.Trigger) => item.triggerid) || [];
        const problem =
          problemList?.filter((item: RK_API.Problem) => arr.includes(item.objectid)) || [];
        const data = groupedData(problem, 'severity');
        const obj = option2enum(SEVERITIES);
        return problem.length ? (
          Object.entries(data).map(([key, val]) => (
            <Tag
              style={{ cursor: 'pointer' }}
              key={key}
              color={obj[key].tagColor}
              onClick={() => passParamsToPage('/alarm/event', { hostids: entity.hostid })}
            >
              {val.length}
            </Tag>
          ))
        ) : (
          <>-</>
        );
      },
    },

    {
      title: '标签',
      dataIndex: 'rk_tags',
      width: 200,

      // @ts-ignore
      valueType: 'tagsSelectSetter',
      fieldProps: {
        tags: hostTags,
      },
      render(dom, entity) {
        const { tags = [], inheritedTags = [] } = entity;
        const mixTags = [...inheritedTags, ...tags];
        if (!mixTags.length) return <>-</>;
        return (mixTags as RK_API.TemplateTag[]).map((item, index) => (
          <Tag key={index}>
            {item?.tag}: {item?.value}
          </Tag>
        ));
      },
    },
    // {
    //   title: '查看维修中的主机',
    //   dataIndex: 'maintenance_status',
    //   hideInTable: true,
    //   valueType: 'switch',
    //   search: {
    //     transform: (value) => ({ maintenance_status: value ? '1' : '0' }),
    //   },
    //   formItemProps: {
    //     className: 'form-label-warp',
    //   },
    //   fieldProps: () => ({
    //     onChange: (value) => {
    //       if (!value) formRef.current?.setFieldsValue({ withProblemsSuppressed: false });
    //     },
    //   }),
    // },
    // {
    //   title: '显示处理的问题',
    //   dataIndex: 'withProblemsSuppressed',
    //   hideInTable: true,
    //   valueType: 'switch',
    //   formItemProps: {
    //     dependencies: ['maintenance_status'],
    //     className: 'form-label-warp',
    //   },
    //   fieldProps: (form) => ({
    //     disabled: !form.getFieldValue('maintenance_status'),
    //   }),
    // },
    {
      title: '严重性',
      width: 100,
      dataIndex: 'severities',
      hideInTable: true,
      valueType: 'checkbox',
      colSize: 2,
      fieldProps: {
        options: SEVERITIES,
      },
      initialValue: queryParams.getAll('severities'),
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProConfigProvider valueTypeMap={valueTypeMap}>
        <ProTable<RK_API.Host>
          {...defaultTableConfig}
          search={SearchOptionRender}
          onSubmit={syncToUrl}
          rowKey="hostid"
          formRef={formRef}
          actionRef={tableRef}
          columns={columns}
          headerTitle="主机列表"
          // dataSource={dataSourceRef.current}
          request={async (params) => {
            const {
              name,
              groupids,
              evaltype,
              tags = [],
              status,
              severities = [],
              maintenance_status,
              withProblemsSuppressed,
              rk_tags,
              rkzl_monitorObjectSubtypeId,
            } = params;
            const res = await queryPagingTable<RK_API.Host>(
              {
                search: {
                  name,
                },
                filter: {
                  status,
                  rkzl_monitorObjectSubtypeId,
                },
                maintenance_status,
                withProblemsSuppressed,
                severities: severities.length ? severities : null,
                groupids: groupids?.length ? groupids : null,
                evaltype,
                tags: rk_tags,

                method: 'host.get',
                sortfield: ['name', 'hostid'],
                output: ['hostid', 'name', 'status', 'severities'],
                inheritedTags: true,
              },
              zabbixList,
            );
            const hostIds = res.data?.map((item: RK_API.Item) => item.hostid) || [];
            const { current = 1, pageSize = 10 } = params;
            const index = (current - 1) * pageSize;
            const queryIds = hostIds.splice(index, pageSize);
            getTriggers(queryIds);

            const hostList = await getHosts({
              search: {
                name,
              },
              filter: {
                status,
              },
              maintenance_status,
              withProblemsSuppressed,
              severities: severities.length ? severities : null,
              groupids: groupids?.length ? groupids : null,
              evaltype,
              tags: tags.filter((item: RK_API.TemplateTag) => item.tag),
              inheritedTags: true,
              selectInterfaces: 'extend',
              selectItems: 'extend',
              selectTags: 'extend',
              selectInheritedTags: 'extend',
              selectTriggers: 'extend',
              method: 'host.get',
              sortfield: ['name', 'hostid'],
              hostids: queryIds,
            });
            return {
              ...res,
              data: hostList,
            };
          }}
        />
      </ProConfigProvider>
      <InterfaceModal
        onOpenChange={setModalVisit}
        open={modalVisit}
        initialValues={interfaceArr.current}
      />
    </PageContainer>
  );
});

export default HostMonitor;
