import '@/utils/theme';
import { Area, AreaConfig } from '@ant-design/charts';

export type AreaProps = Partial<AreaConfig> & {
  data: AreaConfig['data'];
};
const RkArea: React.FC<AreaProps> = ({ data, ...rest }) => {
  const enableScrollbar = data.length > 12;
  const config: AreaConfig = {
    data,
    xField: 'time',
    yField: 'value',
    xAxis: {
      tickCount: 12,
      label: {
        autoHide: true,
        autoRotate: false,
      },
    },
    yAxis: {
      grid: {
        line: { style: { stroke: '#F5F5F5' } },
      },
      line: null,
      tickLine: null,
    },
    point: {
      size: 5,
      shape: 'dot',
      style: {
        fill: 'white',
        lineWidth: 2,
      },
    },
    areaStyle: () => ({ fillOpacity: 0.2 }),
    scrollbar: enableScrollbar ? { type: 'horizontal' } : undefined,
    slider: enableScrollbar ? { start: 0.5, end: 1 } : undefined,
    ...rest,
  };

  return <Area {...config} />;
};

export default RkArea;
