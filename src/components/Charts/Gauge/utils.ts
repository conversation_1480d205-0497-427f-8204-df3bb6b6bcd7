// 线性插值两个 HEX 颜色
export function interpolateColor(color1: string, color2: string, factor: number): string {
  const hex = (color: string) => {
    const c = color.replace('#', '');
    return [
      parseInt(c.substring(0, 2), 16),
      parseInt(c.substring(2, 4), 16),
      parseInt(c.substring(4, 6), 16),
    ];
  };

  const [r1, g1, b1] = hex(color1);
  const [r2, g2, b2] = hex(color2);
  const r = Math.round(r1 + (r2 - r1) * factor);
  const g = Math.round(g1 + (g2 - g1) * factor);
  const b = Math.round(b1 + (b2 - b1) * factor);
  return `rgb(${r},${g},${b})`;
}

// 自定义函数：根据 percent 获取渐变色
export function getGradientColor(percent: number): string {
  if (percent <= 0.5) {
    return interpolateColor('#37c8c7', '#a6977d', percent / 0.9);
  } else {
    return interpolateColor('#a6977d', '#f27c55', (percent - 0.5) / 0.5);
  }
}
